"""
海博API测试脚本
用于验证API签名和请求是否正确
"""

import json
from haibo_api import HaiboAPI
from config import TEST_ORDER_IDS, TEST_CARRIER_SHOP_IDS


def test_signature_generation():
    """测试签名生成是否正确"""
    api = HaiboAPI()

    # 使用文档中的示例参数测试签名
    test_params = {
        'developerId': 'test',
        'timestamp': 1477395862,
        'version': '1.0',
        'number': '123',
        'string': '测试',
        'double': '123.123',
        'boolean': 'true',
        'empty': ''
    }

    # 调试：显示过滤后的参数
    filtered_params = {}
    for k, v in test_params.items():
        if k != 'sign' and v is not None and str(v) != '':
            filtered_params[k] = str(v)

    print(f"过滤后的参数: {filtered_params}")

    # 调试：显示排序后的参数
    sorted_params = sorted(filtered_params.items())
    print(f"排序后的参数: {sorted_params}")

    # 调试：显示拼接的参数字符串
    param_string = ''.join([f"{k}{v}" for k, v in sorted_params])
    print(f"拼接的参数字符串: {param_string}")

    # 调试：显示加密前的字符串
    sign_string = api.secret + param_string
    print(f"加密前的字符串: {sign_string}")
    print(f"期望的加密前字符串: testbooleantruedeveloperIdtestdouble123.123number123string测试timestamp1477395862version1.0")

    signature = api._generate_signature(test_params)
    expected_signature = '8943ba698f4b009f80dc2fd69ff9b313381263bd'

    print(f"生成的签名: {signature}")
    print(f"期望的签名: {expected_signature}")
    print(f"签名验证: {'✓ 通过' if signature == expected_signature else '✗ 失败'}")

    return signature == expected_signature


def test_api_call():
    """测试API调用"""
    api = HaiboAPI()
    
    print("\n=== 测试API调用 ===")
    
    # 测试只使用订单号的情况
    order_id = TEST_ORDER_IDS[0]
    print(f"\n1. 测试订单号: {order_id}")
    
    try:
        response = api.get_rider_location(order_id)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"响应内容: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            if api.validate_response(response):
                print("✓ API调用成功")
            else:
                print("✗ API返回错误")
                
        except json.JSONDecodeError:
            print(f"响应不是有效的JSON: {response.text}")
            
    except Exception as e:
        print(f"✗ API调用异常: {e}")
    
    # 测试使用订单号和门店ID的情况
    carrier_shop_id = TEST_CARRIER_SHOP_IDS[0]
    print(f"\n2. 测试订单号: {order_id}, 门店ID: {carrier_shop_id}")
    
    try:
        response = api.get_rider_location(order_id, carrier_shop_id)
        print(f"HTTP状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"响应内容: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            if api.validate_response(response):
                print("✓ API调用成功")
            else:
                print("✗ API返回错误")
                
        except json.JSONDecodeError:
            print(f"响应不是有效的JSON: {response.text}")
            
    except Exception as e:
        print(f"✗ API调用异常: {e}")


def main():
    """主函数"""
    print("=== 海博API测试 ===")
    
    # 测试签名生成
    print("\n1. 测试签名生成")
    signature_ok = test_signature_generation()
    
    if not signature_ok:
        print("⚠️  签名生成测试失败，请检查签名算法")
        return
    
    # 测试API调用
    test_api_call()
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    main()
